# 协程示例，为每个合约创建 task
from tqsdk import TqApi
from tqsdk import TqA<PERSON>, TqAuth, TargetPosTask
from tqsdk.ta import MA

api = TqApi(auth=TqAuth("快期账户", "账户密码"))  # 构造 api 实例

async def demo(SYMBOL, SHORT, LONG):
    """
    双均线策略 -- SYMBOL: 合约, SHORT: 短周期, LONG: 长周期
    """
    data_length = LONG + 2  # k线数据长度
    # get_kline_serial 支持 await 异步写法，这里会订阅 K 线，等到收到 k 线数据才返回
    klines = await api.get_kline_serial(SYMBOL, duration_seconds=60, data_length=data_length)
    target_pos = TargetPosTask(api, SYMBOL)
    async with api.register_update_notify() as update_chan:
        async for _ in update_chan:
            if api.is_changing(klines.iloc[-1], "datetime"):
                short_avg = ma(klines["close"], SHORT)  # 短周期
                long_avg = ma(klines["close"], LONG)  # 长周期
                if long_avg.iloc[-2] < short_avg.iloc[-2] and long_avg.iloc[-1] > short_avg.iloc[-1]:
                    target_pos.set_target_volume(-3)
                    print("均线下穿，做空")
                if short_avg.iloc[-2] < long_avg.iloc[-2] and short_avg.iloc[-1] > long_avg.iloc[-1]:
                    target_pos.set_target_volume(3)
                    print("均线上穿，做多")

# 为每个合约创建异步任务
api.create_task(demo("SHFE.rb2107", 30, 60))
api.create_task(demo("DCE.m2109", 30, 60))
api.create_task(demo("DCE.jd2109", 30, 60))

while True:
    api.wait_update()